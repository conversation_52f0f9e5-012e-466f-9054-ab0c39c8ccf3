import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { SignupStateService } from '../../services/signup-state.service';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-profile-completion-message',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div
      *ngIf="showMessage"
      class="message-overlay"
      [@slideIn]="showMessage ? 'in' : 'out'"
    >
      <div class="message-container">
        <!-- Background Effects -->
        <div class="bg-effects">
          <div class="floating-orb orb-1"></div>
          <div class="floating-orb orb-2"></div>
          <div class="floating-orb orb-3"></div>
        </div>

        <!-- Le message se ferme quand l'utilisateur clique sur "Parfait, j'ai compris" -->

        <!-- Main Content -->
        <div class="message-content">
          <!-- Icon Section -->
          <div class="icon-section">
            <div class="icon-wrapper">
              <mat-icon class="main-icon">verified_user</mat-icon>
              <div class="icon-glow"></div>
            </div>
          </div>

          <!-- Text Section -->
          <div class="text-section">
            <h2 class="welcome-title">Bienvenue sur Kairos IT !</h2>
            <div class="title-underline"></div>

            <div class="status-card">
              <div class="status-icon">
                <mat-icon>schedule</mat-icon>
              </div>
              <div class="status-content">
                <h3 class="status-title">Profil en cours de traitement</h3>
                <p class="status-description">
                  Votre profil sera complété dans quelques minutes
                </p>
              </div>
            </div>

            <div class="info-section">
              <div class="info-item">
                <mat-icon class="info-icon">psychology</mat-icon>
                <span>Analyse intelligente de votre CV</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">auto_awesome</mat-icon>
                <span>Optimisation automatique du profil</span>
              </div>
              <div class="info-item">
                <mat-icon class="info-icon">notifications_active</mat-icon>
                <span>Notification dès que c'est prêt</span>
              </div>
            </div>
          </div>

          <!-- Progress Section -->
          <div class="progress-section">
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <p class="progress-text">Traitement en cours...</p>
          </div>

          <!-- Action Section -->
          <div class="action-section">
            <button
              class="primary-button"
              (click)="acknowledgeMessage()"
            >
              <span>Parfait, j'ai compris</span>
              <mat-icon>check</mat-icon>
            </button>

            <button
              class="secondary-button"
              (click)="exploreWhileWaiting()"
            >
              <mat-icon>explore</mat-icon>
              <span>Explorer en attendant</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./profile-completion-message.component.scss'],
  animations: [
    trigger('slideIn', [
      state('in', style({ transform: 'translateY(0)', opacity: 1 })),
      state('out', style({ transform: 'translateY(-100%)', opacity: 0 })),
      transition('out => in', [
        animate('0.3s ease-out')
      ]),
      transition('in => out', [
        animate('0.3s ease-in')
      ])
    ])
  ]
})
export class ProfileCompletionMessageComponent implements OnInit, OnDestroy {
  showMessage = false;
  private autoCloseTimer?: number;
  private authSubscription?: Subscription;

  constructor(
    private signupStateService: SignupStateService,
    private oidcSecurityService: OidcSecurityService
  ) {}

  ngOnInit(): void {
    console.log('ProfileCompletionMessage - ngOnInit called');

    // Écouter les événements de connexion
    this.authSubscription = this.oidcSecurityService.checkAuth().pipe(
      filter(loginResponse => loginResponse.isAuthenticated)
    ).subscribe(loginResponse => {
      console.log('User authenticated, checking if should show profile message');

      // Vérifier si l'utilisateur vient de s'inscrire
      const shouldShow = this.signupStateService.checkAndShowMessageAfterLogin();

      if (shouldShow) {
        console.log('User just signed up and logged in - showing profile completion message');
        this.showMessage = true;
      } else {
        console.log('User logged in but did not just sign up - no message to show');
      }
    });

    // Vérifier aussi immédiatement si l'utilisateur est déjà connecté
    this.oidcSecurityService.checkAuth().subscribe(loginResponse => {
      if (loginResponse.isAuthenticated) {
        console.log('User already authenticated, checking if should show profile message');
        const shouldShow = this.signupStateService.checkAndShowMessageAfterLogin();

        if (shouldShow) {
          console.log('User just signed up and is authenticated - showing profile completion message');
          this.showMessage = true;
        }
      }
    });
  }

  ngOnDestroy(): void {
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }

    // Nettoyer l'abonnement aux événements d'authentification
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  closeMessage(): void {
    console.log('closeMessage called - hiding message');
    this.showMessage = false;

    // Nettoyer l'état d'inscription après fermeture
    this.signupStateService.markProfileMessageShown();

    console.log('Message fermé et état nettoyé');

    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }

  acknowledgeMessage(): void {
    console.log('User acknowledged the message - closing message');
    // Fermer le message quand l'utilisateur clique sur "Parfait, j'ai compris"
    this.closeMessage();
  }

  exploreWhileWaiting(): void {
    console.log('User wants to explore while waiting - message stays visible');
    // Le message reste affiché, l'utilisateur peut continuer à naviguer
    // Optionnel: rediriger vers une page spécifique
  }
}
