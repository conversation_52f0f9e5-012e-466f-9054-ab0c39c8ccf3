<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Message de Profil - Kairos IT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #001040FF;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #001040FF;
        }

        .test-section h2 {
            color: #001040FF;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #001040FF;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ff6600;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .status-display {
            background: #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .status-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 600;
            color: #495057;
        }

        .status-value {
            font-family: monospace;
            background: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #ced4da;
        }

        .status-true {
            color: #28a745;
            background: #d4edda !important;
            border-color: #c3e6cb !important;
        }

        .status-false {
            color: #dc3545;
            background: #f8d7da !important;
            border-color: #f5c6cb !important;
        }

        .logs {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid #333;
        }

        .message-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 16, 64, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        }

        .message-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .message-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #001040FF, #ff6600);
        }

        .message-icon {
            font-size: 4em;
            color: #001040FF;
            margin-bottom: 20px;
        }

        .message-title {
            color: #001040FF;
            font-size: 2em;
            margin-bottom: 15px;
        }

        .message-text {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .message-button {
            background: #001040FF;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .message-button:hover {
            background: #000830;
            transform: translateY(-2px);
        }

        .hidden {
            display: none;
        }

        @media (max-width: 600px) {
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
            
            .status-item {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Message de Profil</h1>
            <p>Simulation du flux Signup → Login → Message</p>
        </div>

        <div class="content">
            <div class="test-section">
                <h2>📊 État Actuel</h2>
                <div class="status-display">
                    <div class="status-item">
                        <span class="status-label">Signup Completed:</span>
                        <span class="status-value" id="signup-status">false</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Message Visible:</span>
                        <span class="status-value" id="message-status">false</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">LocalStorage:</span>
                        <span class="status-value" id="localstorage-status">null</span>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h2>🎮 Actions de Test</h2>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="simulateSignup()">
                        👤 Simuler Signup
                    </button>
                    <button class="btn btn-success" onclick="simulateLogin()">
                        🔑 Simuler Login
                    </button>
                    <button class="btn btn-warning" onclick="showMessage()">
                        💬 Afficher Message
                    </button>
                    <button class="btn btn-danger" onclick="resetState()">
                        🗑️ Reset État
                    </button>
                </div>
            </div>

            <div class="test-section">
                <h2>📝 Logs</h2>
                <div class="logs" id="logs"></div>
            </div>
        </div>
    </div>

    <!-- Message de profil -->
    <div class="message-overlay hidden" id="profileMessage">
        <div class="message-container">
            <div class="message-icon">✅</div>
            <h2 class="message-title">Bienvenue sur Kairos IT !</h2>
            <p class="message-text">
                Votre profil sera complété dans quelques minutes.<br>
                Notre IA analyse votre CV pour optimiser votre profil automatiquement.
            </p>
            <button class="message-button" onclick="acknowledgeMessage()">
                Parfait, j'ai compris
            </button>
        </div>
    </div>

    <script>
        let logs = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.unshift(`[${timestamp}] ${message}`);
            
            if (logs.length > 20) {
                logs = logs.slice(0, 20);
            }
            
            updateLogsDisplay();
        }

        function updateLogsDisplay() {
            const logsContainer = document.getElementById('logs');
            logsContainer.innerHTML = logs.map(log => 
                `<div class="log-entry">${log}</div>`
            ).join('');
        }

        function updateStatus() {
            const signupCompleted = localStorage.getItem('signup_completed') === 'true';
            const messageVisible = !document.getElementById('profileMessage').classList.contains('hidden');
            const localStorageValue = localStorage.getItem('signup_completed') || 'null';

            document.getElementById('signup-status').textContent = signupCompleted;
            document.getElementById('signup-status').className = `status-value ${signupCompleted ? 'status-true' : 'status-false'}`;

            document.getElementById('message-status').textContent = messageVisible;
            document.getElementById('message-status').className = `status-value ${messageVisible ? 'status-true' : 'status-false'}`;

            document.getElementById('localstorage-status').textContent = localStorageValue;
        }

        function simulateSignup() {
            addLog('🚀 Simulation du signup...');
            localStorage.setItem('signup_completed', 'true');
            addLog('✅ Signup complété - signup_completed: true');
            updateStatus();
        }

        function simulateLogin() {
            addLog('🔑 Simulation du login...');
            const signupCompleted = localStorage.getItem('signup_completed') === 'true';
            
            if (signupCompleted) {
                addLog('✅ Login réussi - utilisateur vient de s\'inscrire');
                addLog('🎉 Conditions remplies pour afficher le message !');
                showMessage();
            } else {
                addLog('❌ Login réussi mais utilisateur n\'a pas fait de signup récent');
            }
        }

        function showMessage() {
            addLog('💬 Affichage du message de profil...');
            document.getElementById('profileMessage').classList.remove('hidden');
            updateStatus();
        }

        function acknowledgeMessage() {
            addLog('👍 Utilisateur a cliqué sur "Parfait, j\'ai compris"');
            document.getElementById('profileMessage').classList.add('hidden');
            localStorage.removeItem('signup_completed');
            addLog('🧹 État nettoyé - signup_completed supprimé');
            updateStatus();
        }

        function resetState() {
            addLog('🗑️ Reset de l\'état...');
            localStorage.removeItem('signup_completed');
            document.getElementById('profileMessage').classList.add('hidden');
            addLog('✅ État réinitialisé');
            updateStatus();
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🎯 Page de test chargée');
            updateStatus();
            
            // Vérifier si le message doit s'afficher au chargement
            const signupCompleted = localStorage.getItem('signup_completed') === 'true';
            if (signupCompleted) {
                addLog('🔍 signup_completed détecté au chargement');
                addLog('💡 Simulez un login pour voir le message');
            }
        });
    </script>
</body>
</html>
